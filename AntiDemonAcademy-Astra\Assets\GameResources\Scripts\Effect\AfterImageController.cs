using System.Collections.Generic;
using UnityEngine;
using System.Collections;
using Studio.CombatSystem;

namespace Studio.Effect {
    public class AfterImageController : MonoBehaviour
    {
        private StateController sc;
        public bool openAfterImage = false;
        public bool fadeOut = false;

        public GameObject afterImagePrefab;
        private Transform afterImagePool;
        private GameObject defaultAfterImage;

        private Animator anim;

        public int initAfterImageCount = 10; // 最大殘影數量
        public float keepTime = 1f; // 殘影持續時間
        public int intervalFrame = 15; // 間隔多少幀
        private int initIntervalFrame = 15;
        private int frameCount = 0;
        private int coroutineCount = 0;

        [SerializeField]
        private List<AfterImage> afterImageList;
        [SerializeField]
        private List<AfterImage> usedAfterImageList;

        private void Awake() {
            sc = GetComponent<StateController>();
            afterImageList = new List<AfterImage>();
            usedAfterImageList = new List<AfterImage>();

            anim = GetComponent<Animator>();
            initIntervalFrame = intervalFrame;

            afterImagePool = new GameObject("AfterImagePool").transform;

            defaultAfterImage = Instantiate(afterImagePrefab, afterImagePool);            
            
            for (int i = 0; i < initAfterImageCount; i++) {
                CreateAfterImage();
            }
        }

        private void OnEnable() {
            sc.onDead.AddListener(AfterImageDisable);
            sc.onPerfectDodge += TriggerAfterImage;
        }

        private void OnDisable() {
            sc.onDead.RemoveListener(AfterImageDisable);
            sc.onPerfectDodge -= TriggerAfterImage;
        }

        #region Sys
        private void Update() {
            if (openAfterImage) {
                frameCount++;

                if (frameCount % intervalFrame == 0) {
                    frameCount = 0;
                    ShowAfterImage();
                }
            }

            UpdateAfterImage();
        }
        #endregion

        #region Public
        public void TriggerAfterImage() {
            StartCoroutine(AfterImageCoroutine(2));
        }

        public IEnumerator AfterImageCoroutine(float duration) {
            AfterImageEnable();
            yield return new WaitForSeconds(duration);
            AfterImageDisable();
        }

        public IEnumerator AfterImageCoroutine(int m_intervalFrame, float duration) {
            intervalFrame = m_intervalFrame;
            AfterImageEnable();
            yield return new WaitForSeconds(duration);
            intervalFrame = initIntervalFrame;
            AfterImageDisable();
        }

        public IEnumerator AfterImageCoroutine(int count) {
            AfterImageEnable();

            while (coroutineCount < count)
                yield return null;

            AfterImageDisable();
        }
        #endregion

        #region Event
        private void AfterImageEnable() {
            openAfterImage = true;
            frameCount = 0;
            coroutineCount = 0;
        }

        private void AfterImageDisable() {
            openAfterImage = false;
        }

        private void OnStartAfterImage() {
            AfterImageEnable();
        }

        private void OnStopAfterImage() {
            AfterImageDisable();
        }
        #endregion

        #region Private
        private AfterImage CreateAfterImage() {
            AfterImage afterImage = new AfterImage(
                Instantiate(defaultAfterImage, afterImagePool)
            );

            afterImageList.Add(afterImage);

            return afterImage;
        }

        public void ShowAfterImage()
        {
            AfterImage afterImage;

            if (afterImageList.Count == 0)
            {
                afterImage = CreateAfterImage();
            }
            else
            {
                afterImage = afterImageList[0];
            }

            coroutineCount++;

            afterImage.obj.layer = LayerMask.NameToLayer("AfterImage");
            // afterImage.SetAlpha(0.2f);
            afterImage.SetAlpha(1);
            afterImage.anim.Play(anim.GetCurrentAnimatorClipInfo(0)[0].clip.name, 0, anim.GetCurrentAnimatorStateInfo(0).normalizedTime);
            afterImage.anim.speed = 0;
            afterImage.startTime = Time.time;

            usedAfterImageList.Add(afterImage);
            afterImageList.Remove(afterImage);

            
            afterImage.trans.position = transform.position;
            afterImage.trans.forward = transform.forward;
        }

        private void UpdateAfterImage() {
            for (int i = 0; i < usedAfterImageList.Count; i++) {
                float time = Time.time - usedAfterImageList[i].startTime;

                if (time >= keepTime) {
                    usedAfterImageList[i].obj.layer = LayerMask.NameToLayer("Hidden");
                    afterImageList.Add(usedAfterImageList[i]);
                    usedAfterImageList.Remove(usedAfterImageList[i]);
                    i--;
                }
                else if (fadeOut) {
                    // usedAfterImageList[i].SetAlpha((1 - time / keepTime) * 0.2f);
                    usedAfterImageList[i].SetAlpha(1 - time / keepTime);
                }
            }

            for (int i = 0; i < afterImageList.Count; i++) {
                if (afterImageList[i].obj.layer == LayerMask.NameToLayer("AfterImage")) {
                    afterImageList[i].obj.layer = LayerMask.NameToLayer("Hidden");
                }
            }
        }
        #endregion
    }

    [System.Serializable]
    class AfterImage {
        public GameObject obj;
        public Transform trans;
        public GameObject model;
        public Animator anim;
        private Material[] materials;
        public float startTime;

        public AfterImage(GameObject obj) {
            this.obj = obj;
            this.obj.layer = LayerMask.NameToLayer("Hidden");
            this.trans = obj.transform;
            this.anim = obj.GetComponent<Animator>();
            SkinnedMeshRenderer skinMesh = this.trans.GetComponentInChildren<SkinnedMeshRenderer>();
            this.materials = skinMesh.materials;
            this.model = skinMesh.gameObject;
        }

        public void SetAlpha(float value) {
            for (int i = 0; i < this.materials.Length; i++) {
                this.materials[i].SetFloat("_alpha", value);
            }
        }
    }
}
