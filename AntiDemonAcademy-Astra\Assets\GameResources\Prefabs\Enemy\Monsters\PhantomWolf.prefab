%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1001 &7768048253832153071
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 953016545528404752, guid: feed94263c97fd447922e373178ad0be, type: 3}
      propertyPath: m_LocalPosition.x
      value: 99.55399
      objectReference: {fileID: 0}
    - target: {fileID: 953016545528404752, guid: feed94263c97fd447922e373178ad0be, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.00000047683716
      objectReference: {fileID: 0}
    - target: {fileID: 953016545528404752, guid: feed94263c97fd447922e373178ad0be, type: 3}
      propertyPath: m_LocalPosition.z
      value: 94.74942
      objectReference: {fileID: 0}
    - target: {fileID: 953016545528404752, guid: feed94263c97fd447922e373178ad0be, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 953016545528404752, guid: feed94263c97fd447922e373178ad0be, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 953016545528404752, guid: feed94263c97fd447922e373178ad0be, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 953016545528404752, guid: feed94263c97fd447922e373178ad0be, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 953016545528404752, guid: feed94263c97fd447922e373178ad0be, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 953016545528404752, guid: feed94263c97fd447922e373178ad0be, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 953016545528404752, guid: feed94263c97fd447922e373178ad0be, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1001960436763056425, guid: feed94263c97fd447922e373178ad0be, type: 3}
      propertyPath: lookAtTrans
      value: 
      objectReference: {fileID: 5980367714379284366}
    - target: {fileID: 2023124117330753415, guid: feed94263c97fd447922e373178ad0be, type: 3}
      propertyPath: m_Name
      value: PhantomWolf
      objectReference: {fileID: 0}
    - target: {fileID: 3585924642377355874, guid: feed94263c97fd447922e373178ad0be, type: 3}
      propertyPath: attrDB
      value: 
      objectReference: {fileID: 11400000, guid: 2e6df57fedc101848b8e554b81b877b6, type: 2}
    - target: {fileID: 4325423249841817299, guid: feed94263c97fd447922e373178ad0be, type: 3}
      propertyPath: applyRootMotion
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6120199453695925616, guid: feed94263c97fd447922e373178ad0be, type: 3}
      propertyPath: rends.Array.size
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6120199453695925616, guid: feed94263c97fd447922e373178ad0be, type: 3}
      propertyPath: rends.Array.data[0]
      value: 
      objectReference: {fileID: 6516342186579725695}
    - target: {fileID: 8250794822277386717, guid: feed94263c97fd447922e373178ad0be, type: 3}
      propertyPath: vfxRoot
      value: 
      objectReference: {fileID: 3405400474577348327}
    - target: {fileID: 8753908722455318957, guid: feed94263c97fd447922e373178ad0be, type: 3}
      propertyPath: m_Avatar
      value: 
      objectReference: {fileID: 9000000, guid: 76ac524629e7fa547bf36ad076e60198, type: 3}
    - target: {fileID: 8753908722455318957, guid: feed94263c97fd447922e373178ad0be, type: 3}
      propertyPath: m_Controller
      value: 
      objectReference: {fileID: 9100000, guid: c6e91e251b1fafb49837bc07a5a2969a, type: 2}
    - target: {fileID: 8753908722455318957, guid: feed94263c97fd447922e373178ad0be, type: 3}
      propertyPath: m_ApplyRootMotion
      value: 1
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects:
    - targetCorrespondingSourceObject: {fileID: 953016545528404752, guid: feed94263c97fd447922e373178ad0be, type: 3}
      insertIndex: -1
      addedObject: {fileID: 9100640372295999804}
    - targetCorrespondingSourceObject: {fileID: 953016545528404752, guid: feed94263c97fd447922e373178ad0be, type: 3}
      insertIndex: -1
      addedObject: {fileID: 3405400474577348327}
    m_AddedComponents:
    - targetCorrespondingSourceObject: {fileID: 2023124117330753415, guid: feed94263c97fd447922e373178ad0be, type: 3}
      insertIndex: -1
      addedObject: {fileID: 7341746066374729020}
    - targetCorrespondingSourceObject: {fileID: 2023124117330753415, guid: feed94263c97fd447922e373178ad0be, type: 3}
      insertIndex: -1
      addedObject: {fileID: 4942010423498211908}
    - targetCorrespondingSourceObject: {fileID: 2023124117330753415, guid: feed94263c97fd447922e373178ad0be, type: 3}
      insertIndex: -1
      addedObject: {fileID: 5947039667049902097}
  m_SourcePrefab: {fileID: 100100000, guid: feed94263c97fd447922e373178ad0be, type: 3}
--- !u!4 &7418675180849339647 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 953016545528404752, guid: feed94263c97fd447922e373178ad0be, type: 3}
  m_PrefabInstance: {fileID: 7768048253832153071}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &8637396182626480232 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 2023124117330753415, guid: feed94263c97fd447922e373178ad0be, type: 3}
  m_PrefabInstance: {fileID: 7768048253832153071}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &7341746066374729020
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8637396182626480232}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8d7b55c7ecdb49a4a89fa5e6f9022861, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  startWhenEnabled: 1
  asynchronousLoad: 0
  pauseWhenDisabled: 0
  restartWhenComplete: 0
  logTaskChanges: 0
  group: 0
  resetValuesOnRestart: 0
  externalBehavior: {fileID: 0}
  mBehaviorSource:
    behaviorName: Behavior
    behaviorDescription: 
    mTaskData:
      types: []
      parentIndex: 
      startIndex: 
      variableStartIndex: 
      JSONSerialization: '{"EntryTask":{"Type":"BehaviorDesigner.Runtime.Tasks.EntryTask","NodeData":{"Offset":"(-167.3953,62.9986877)"},"ID":0,"Name":"Entry","Instant":true},"RootTask":{"Type":"BehaviorDesigner.Runtime.Tasks.Parallel","NodeData":{"Offset":"(10,180)"},"ID":1,"Name":"Parallel","Instant":true,"AbortTypeabortType":"None","Children":[{"Type":"BehaviorDesigner.Runtime.Tasks.Repeater","NodeData":{"Offset":"(-400,170)"},"ID":2,"Name":"Repeater","Instant":true,"SharedIntcount":{"Type":"BehaviorDesigner.Runtime.SharedInt","Name":null,"Int32mValue":0},"SharedBoolrepeatForever":{"Type":"BehaviorDesigner.Runtime.SharedBool","Name":null,"BooleanmValue":true},"SharedBoolendOnFailure":{"Type":"BehaviorDesigner.Runtime.SharedBool","Name":null,"BooleanmValue":false},"Children":[{"Type":"BehaviorDesigner.Runtime.Tasks.Selector","NodeData":{"Offset":"(0,140)"},"ID":3,"Name":"Selector","Instant":true,"AbortTypeabortType":"Both","Children":[{"Type":"BehaviorDesigner.Runtime.Tasks.Sequence","NodeData":{"Offset":"(-1240,160)"},"ID":4,"Name":"Sequence","Instant":true,"AbortTypeabortType":"Both","Children":[{"Type":"BehaviorDesigner.Runtime.Tasks.Unity.UnityAnimator.IsTag","NodeData":{"Offset":"(-100,140)","Comment":"Hit"},"ID":5,"Name":"Is
        Tag","Instant":true,"SharedGameObjecttargetGameObject":{"Type":"BehaviorDesigner.Runtime.SharedGameObject","Name":null},"SharedIntindex":{"Type":"BehaviorDesigner.Runtime.SharedInt","Name":null,"Int32mValue":0},"SharedStringtag":{"Type":"BehaviorDesigner.Runtime.SharedString","Name":null,"StringmValue":"Hit"}},{"Type":"BehaviorDesigner.Runtime.Tasks.Unity.SharedVariables.SetSharedFloat","NodeData":{"Offset":"(110,140)","Comment":"Set
        View Angle to 360"},"ID":6,"Name":"Set Shared Float","Instant":true,"SharedFloattargetValue":{"Type":"BehaviorDesigner.Runtime.SharedFloat","Name":null,"SinglemValue":360},"SharedFloattargetVariable":{"Type":"BehaviorDesigner.Runtime.SharedFloat","Name":"ViewAngle","IsShared":true,"SinglemValue":360}}]},{"Type":"BehaviorDesigner.Runtime.Tasks.BehaviorTreeReference","NodeData":{"Offset":"(-890,170)","Comment":"Out
        Of Origin"},"ID":7,"Name":"Behavior Tree Reference","Instant":true,"ExternalBehavior[]externalBehaviors":[0],"SharedNamedVariable[]variables":[],"Booleancollapsed":false},{"Type":"BehaviorDesigner.Runtime.Tasks.Sequence","NodeData":{"Offset":"(-420,150)"},"ID":8,"Name":"Sequence","Instant":true,"AbortTypeabortType":"Both","Children":[{"Type":"BehaviorDesigner.Runtime.Tasks.Unity.UnityGameObject.CompareTag","NodeData":{"Offset":"(-320,150)","Comment":"Compare
        Player"},"ID":9,"Name":"Compare Tag","Instant":true,"SharedGameObjecttargetGameObject":{"Type":"BehaviorDesigner.Runtime.SharedGameObject","Name":"Player","IsShared":true},"SharedStringtag":{"Type":"BehaviorDesigner.Runtime.SharedString","Name":null,"StringmValue":"Player"}},{"Type":"BehaviorDesigner.Runtime.Tasks.Unity.UnityAnimator.GetBoolParameter","NodeData":{"Offset":"(-150,150)","Comment":"isGrouned"},"ID":10,"Name":"Get
        Bool Parameter","Instant":true,"SharedGameObjecttargetGameObject":{"Type":"BehaviorDesigner.Runtime.SharedGameObject","Name":null},"SharedStringparamaterName":{"Type":"BehaviorDesigner.Runtime.SharedString","Name":null,"StringmValue":"isGround"},"SharedBoolstoreValue":{"Type":"BehaviorDesigner.Runtime.SharedBool","Name":"isGround","IsShared":true,"BooleanmValue":false}},{"Type":"BehaviorDesigner.Runtime.Tasks.Unity.SharedVariables.CompareSharedBool","NodeData":{"Offset":"(30,150)","Comment":"isGround"},"ID":11,"Name":"Compare
        Shared Bool","Instant":true,"SharedBoolvariable":{"Type":"BehaviorDesigner.Runtime.SharedBool","Name":"isGround","IsShared":true,"BooleanmValue":false},"SharedBoolcompareTo":{"Type":"BehaviorDesigner.Runtime.SharedBool","Name":null,"BooleanmValue":true}},{"Type":"BehaviorDesigner.Runtime.Tasks.Selector","NodeData":{"Offset":"(220,220)"},"ID":12,"Name":"Selector","Instant":true,"AbortTypeabortType":"Both","Children":[{"Type":"BehaviorDesigner.Runtime.Tasks.Sequence","NodeData":{"Offset":"(-80,170)"},"ID":13,"Name":"Sequence","Instant":true,"AbortTypeabortType":"Both","Children":[{"Type":"BehaviorDesigner.Runtime.Tasks.Unity.UnityAnimator.IsTag","NodeData":{"Offset":"(-110,210)","Comment":"Idle"},"ID":14,"Name":"Is
        Tag","Instant":true,"SharedGameObjecttargetGameObject":{"Type":"BehaviorDesigner.Runtime.SharedGameObject","Name":null},"SharedIntindex":{"Type":"BehaviorDesigner.Runtime.SharedInt","Name":null,"Int32mValue":0},"SharedStringtag":{"Type":"BehaviorDesigner.Runtime.SharedString","Name":null,"StringmValue":"idle"}},{"Type":"BehaviorDesigner.Runtime.Tasks.Selector","NodeData":{"Offset":"(60,210)"},"ID":15,"Name":"Selector","Instant":true,"AbortTypeabortType":"Both","Children":[{"Type":"BehaviorDesigner.Runtime.Tasks.Sequence","NodeData":{"Offset":"(-100,150)"},"ID":16,"Name":"Sequence","Instant":true,"AbortTypeabortType":"Both","Children":[{"Type":"BehaviorDesigner.Runtime.Tasks.Unity.UnityTransform.InRange","NodeData":{"Offset":"(-240,160)"},"ID":17,"Name":"In
        Range","Instant":true,"SharedGameObjectselfObject":{"Type":"BehaviorDesigner.Runtime.SharedGameObject","Name":null},"SharedGameObjecttargetObject":{"Type":"BehaviorDesigner.Runtime.SharedGameObject","Name":"Player","IsShared":true},"SharedVector3targetPosition":{"Type":"BehaviorDesigner.Runtime.SharedVector3","Name":null,"Vector3mValue":"(0,0,0)"},"SharedFloatminDistance":{"Type":"BehaviorDesigner.Runtime.SharedFloat","Name":null,"SinglemValue":0},"SharedFloatmaxDistance":{"Type":"BehaviorDesigner.Runtime.SharedFloat","Name":null,"SinglemValue":10},"SharedFloatrangeAngle":{"Type":"BehaviorDesigner.Runtime.SharedFloat","Name":null,"SinglemValue":360},"SharedBoolignoreHeight":{"Type":"BehaviorDesigner.Runtime.SharedBool","Name":null,"BooleanmValue":true}},{"Type":"BehaviorDesigner.Runtime.Tasks.Wait","NodeData":{"Offset":"(-60,170)"},"ID":18,"Name":"Wait","Instant":true,"SharedFloatwaitTime":{"Type":"BehaviorDesigner.Runtime.SharedFloat","Name":null,"SinglemValue":2},"SharedBoolrandomWait":{"Type":"BehaviorDesigner.Runtime.SharedBool","Name":null,"BooleanmValue":false},"SharedFloatrandomWaitMin":{"Type":"BehaviorDesigner.Runtime.SharedFloat","Name":null,"SinglemValue":1},"SharedFloatrandomWaitMax":{"Type":"BehaviorDesigner.Runtime.SharedFloat","Name":null,"SinglemValue":1}},{"Type":"BehaviorDesigner.Runtime.Tasks.Selector","NodeData":{"Offset":"(140,190)"},"ID":19,"Name":"Selector","Instant":true,"AbortTypeabortType":"Both","Children":[{"Type":"BehaviorDesigner.Runtime.Tasks.Sequence","NodeData":{"Offset":"(-220,150)"},"ID":20,"Name":"Sequence","Instant":true,"AbortTypeabortType":"Both","Children":[{"Type":"BehaviorDesigner.Runtime.Tasks.Unity.SharedVariables.CompareSharedBool","NodeData":{"Offset":"(-280,160)"},"ID":21,"Name":"Compare
        Shared Bool","Instant":true,"SharedBoolvariable":{"Type":"BehaviorDesigner.Runtime.SharedBool","Name":"CanAttack","IsShared":true,"BooleanmValue":true},"SharedBoolcompareTo":{"Type":"BehaviorDesigner.Runtime.SharedBool","Name":null,"BooleanmValue":true}},{"Type":"BehaviorDesigner.Runtime.Tasks.RandomSelector","NodeData":{"Offset":"(110,160)","Comment":"Random
        Attack Actions"},"ID":22,"Name":"Random Selector","Instant":true,"Int32seed":0,"BooleanuseSeed":false,"AbortTypeabortType":"Both","Children":[{"Type":"BehaviorDesigner.Runtime.Tasks.Sequence","NodeData":{"Offset":"(-472.353027,270)"},"ID":23,"Name":"Sequence","Instant":true,"Disabled":true,"AbortTypeabortType":"Both","Children":[{"Type":"BehaviorDesigner.Runtime.Tasks.UntilSuccess","NodeData":{"Offset":"(-130,150)"},"ID":24,"Name":"Until
        Success","Instant":true,"Children":[{"Type":"BehaviorDesigner.Runtime.Tasks.Selector","NodeData":{"Offset":"(0,120)"},"ID":25,"Name":"Selector","Instant":true,"AbortTypeabortType":"Both","Children":[{"Type":"BehaviorDesigner.Runtime.Tasks.Unity.UnityTransform.InRange","NodeData":{"Offset":"(-110,130)"},"ID":26,"Name":"In
        Range","Instant":true,"SharedGameObjectselfObject":{"Type":"BehaviorDesigner.Runtime.SharedGameObject","Name":null},"SharedGameObjecttargetObject":{"Type":"BehaviorDesigner.Runtime.SharedGameObject","Name":"Player","IsShared":true},"SharedVector3targetPosition":{"Type":"BehaviorDesigner.Runtime.SharedVector3","Name":null,"Vector3mValue":"(0,0,0)"},"SharedFloatminDistance":{"Type":"BehaviorDesigner.Runtime.SharedFloat","Name":null,"SinglemValue":0},"SharedFloatmaxDistance":{"Type":"BehaviorDesigner.Runtime.SharedFloat","Name":null,"SinglemValue":1.5},"SharedFloatrangeAngle":{"Type":"BehaviorDesigner.Runtime.SharedFloat","Name":null,"SinglemValue":360},"SharedBoolignoreHeight":{"Type":"BehaviorDesigner.Runtime.SharedBool","Name":null,"BooleanmValue":true}},{"Type":"BehaviorDesigner.Runtime.Tasks.Movement.AstarPathfindingProject.Follow","NodeData":{"Offset":"(90,130)"},"ID":27,"Name":"Follow","Instant":true,"SharedGameObjecttarget":{"Type":"BehaviorDesigner.Runtime.SharedGameObject","Name":"Player","IsShared":true},"SharedFloatmoveDistance":{"Type":"BehaviorDesigner.Runtime.SharedFloat","Name":null,"SinglemValue":1},"SharedFloatspeed":{"Type":"BehaviorDesigner.Runtime.SharedFloat","Name":"RunSpeed","IsShared":true,"SinglemValue":4},"SharedBoolstopOnTaskEnd":{"Type":"BehaviorDesigner.Runtime.SharedBool","Name":null,"BooleanmValue":true},"Booleanuse2DMovement":false}]}]},{"Type":"BehaviorDesigner.Runtime.Tasks.Unity.SharedVariables.SetSharedString","NodeData":{"Offset":"(10,150)"},"ID":28,"Name":"Set
        Shared String","Instant":true,"SharedStringtargetValue":{"Type":"BehaviorDesigner.Runtime.SharedString","Name":null,"StringmValue":"attack01"},"SharedStringtargetVariable":{"Type":"BehaviorDesigner.Runtime.SharedString","Name":"lastAction","IsShared":true,"StringmValue":"idle"}},{"Type":"BehaviorDesigner.Runtime.Tasks.Unity.UnityAnimator.Play","NodeData":{"Offset":"(160,170)","Comment":"attack01"},"ID":29,"Name":"Play","Instant":true,"SharedGameObjecttargetGameObject":{"Type":"BehaviorDesigner.Runtime.SharedGameObject","Name":null},"SharedStringstateName":{"Type":"BehaviorDesigner.Runtime.SharedString","Name":null,"StringmValue":"attack01"},"Int32layer":0,"SinglenormalizedTime":0}]},{"Type":"BehaviorDesigner.Runtime.Tasks.Sequence","NodeData":{"Offset":"(97.0587,290)"},"ID":30,"Name":"Sequence","Instant":true,"AbortTypeabortType":"Both","Children":[{"Type":"BehaviorDesigner.Runtime.Tasks.Unity.UnityTransform.InRange","NodeData":{"Offset":"(-115.4162,166.369446)"},"ID":31,"Name":"In
        Range","Instant":true,"Disabled":true,"SharedGameObjectselfObject":{"Type":"BehaviorDesigner.Runtime.SharedGameObject","Name":null},"SharedGameObjecttargetObject":{"Type":"BehaviorDesigner.Runtime.SharedGameObject","Name":"Player","IsShared":true},"SharedVector3targetPosition":{"Type":"BehaviorDesigner.Runtime.SharedVector3","Name":null,"Vector3mValue":"(0,0,0)"},"SharedFloatminDistance":{"Type":"BehaviorDesigner.Runtime.SharedFloat","Name":null,"SinglemValue":0},"SharedFloatmaxDistance":{"Type":"BehaviorDesigner.Runtime.SharedFloat","Name":null,"SinglemValue":5},"SharedFloatrangeAngle":{"Type":"BehaviorDesigner.Runtime.SharedFloat","Name":null,"SinglemValue":360},"SharedBoolignoreHeight":{"Type":"BehaviorDesigner.Runtime.SharedBool","Name":null,"BooleanmValue":true}},{"Type":"BehaviorDesigner.Runtime.Tasks.Unity.SharedVariables.SetSharedString","NodeData":{"Offset":"(67.05876,179.411743)"},"ID":32,"Name":"Set
        Shared String","Instant":true,"SharedStringtargetValue":{"Type":"BehaviorDesigner.Runtime.SharedString","Name":null,"StringmValue":"attack02"},"SharedStringtargetVariable":{"Type":"BehaviorDesigner.Runtime.SharedString","Name":"lastAction","IsShared":true,"StringmValue":"idle"}},{"Type":"BehaviorDesigner.Runtime.Tasks.Unity.UnityAnimator.Play","NodeData":{"Offset":"(231.764618,174.705872)","Comment":"attack02"},"ID":33,"Name":"Play","Instant":true,"SharedGameObjecttargetGameObject":{"Type":"BehaviorDesigner.Runtime.SharedGameObject","Name":null},"SharedStringstateName":{"Type":"BehaviorDesigner.Runtime.SharedString","Name":null,"StringmValue":"attack02"},"Int32layer":0,"SinglenormalizedTime":0}]}]},{"Type":"BehaviorDesigner.Runtime.Tasks.Unity.SharedVariables.SetSharedBool","NodeData":{"Offset":"(450,150)"},"ID":34,"Name":"Set
        Shared Bool","Instant":true,"SharedBooltargetValue":{"Type":"BehaviorDesigner.Runtime.SharedBool","Name":null,"BooleanmValue":false},"SharedBooltargetVariable":{"Type":"BehaviorDesigner.Runtime.SharedBool","Name":"CanAttack","IsShared":true,"BooleanmValue":true}}]},{"Type":"BehaviorDesigner.Runtime.Tasks.Sequence","NodeData":{"Offset":"(490,200)"},"ID":35,"Name":"Sequence","Instant":true,"AbortTypeabortType":"Both","Children":[{"Type":"BehaviorDesigner.Runtime.Tasks.Unity.UnityTransform.InRange","NodeData":{"Offset":"(-70,130)"},"ID":36,"Name":"In
        Range","Instant":true,"SharedGameObjectselfObject":{"Type":"BehaviorDesigner.Runtime.SharedGameObject","Name":null},"SharedGameObjecttargetObject":{"Type":"BehaviorDesigner.Runtime.SharedGameObject","Name":"Player","IsShared":true},"SharedVector3targetPosition":{"Type":"BehaviorDesigner.Runtime.SharedVector3","Name":null,"Vector3mValue":"(0,0,0)"},"SharedFloatminDistance":{"Type":"BehaviorDesigner.Runtime.SharedFloat","Name":null,"SinglemValue":6},"SharedFloatmaxDistance":{"Type":"BehaviorDesigner.Runtime.SharedFloat","Name":null,"SinglemValue":10},"SharedFloatrangeAngle":{"Type":"BehaviorDesigner.Runtime.SharedFloat","Name":null,"SinglemValue":360},"SharedBoolignoreHeight":{"Type":"BehaviorDesigner.Runtime.SharedBool","Name":null,"BooleanmValue":true}},{"Type":"BehaviorDesigner.Runtime.Tasks.Unity.UnityAnimator.Play","NodeData":{"Offset":"(90,120)","Comment":"Move
        Forward"},"ID":37,"Name":"Play","Instant":true,"SharedGameObjecttargetGameObject":{"Type":"BehaviorDesigner.Runtime.SharedGameObject","Name":null},"SharedStringstateName":{"Type":"BehaviorDesigner.Runtime.SharedString","Name":null,"StringmValue":"MoveForward"},"Int32layer":0,"SinglenormalizedTime":0}]},{"Type":"BehaviorDesigner.Runtime.Tasks.Sequence","NodeData":{"Offset":"(830,190)"},"ID":38,"Name":"Sequence","Instant":true,"AbortTypeabortType":"Both","Children":[{"Type":"BehaviorDesigner.Runtime.Tasks.Unity.UnityTransform.InRange","NodeData":{"Offset":"(-70,130)"},"ID":39,"Name":"In
        Range","Instant":true,"SharedGameObjectselfObject":{"Type":"BehaviorDesigner.Runtime.SharedGameObject","Name":null},"SharedGameObjecttargetObject":{"Type":"BehaviorDesigner.Runtime.SharedGameObject","Name":"Player","IsShared":true},"SharedVector3targetPosition":{"Type":"BehaviorDesigner.Runtime.SharedVector3","Name":null,"Vector3mValue":"(0,0,0)"},"SharedFloatminDistance":{"Type":"BehaviorDesigner.Runtime.SharedFloat","Name":null,"SinglemValue":0},"SharedFloatmaxDistance":{"Type":"BehaviorDesigner.Runtime.SharedFloat","Name":null,"SinglemValue":3},"SharedFloatrangeAngle":{"Type":"BehaviorDesigner.Runtime.SharedFloat","Name":null,"SinglemValue":360},"SharedBoolignoreHeight":{"Type":"BehaviorDesigner.Runtime.SharedBool","Name":null,"BooleanmValue":true}},{"Type":"BehaviorDesigner.Runtime.Tasks.Unity.UnityAnimator.Play","NodeData":{"Offset":"(90,120)","Comment":"Move
        Back"},"ID":40,"Name":"Play","Instant":true,"SharedGameObjecttargetGameObject":{"Type":"BehaviorDesigner.Runtime.SharedGameObject","Name":null},"SharedStringstateName":{"Type":"BehaviorDesigner.Runtime.SharedString","Name":null,"StringmValue":"MoveBack"},"Int32layer":0,"SinglenormalizedTime":0}]},{"Type":"BehaviorDesigner.Runtime.Tasks.RandomSelector","NodeData":{"Offset":"(1290,160)","Comment":"Random
        Move"},"ID":41,"Name":"Random Selector","Instant":true,"Int32seed":0,"BooleanuseSeed":false,"AbortTypeabortType":"Both","Children":[{"Type":"BehaviorDesigner.Runtime.Tasks.Sequence","NodeData":{"Offset":"(-220,250)"},"ID":42,"Name":"Sequence","Instant":true,"AbortTypeabortType":"Both","Children":[{"Type":"BehaviorDesigner.Runtime.Tasks.RandomProbability","NodeData":{"Offset":"(-100,130)"},"ID":43,"Name":"Random
        Probability","Instant":true,"SharedFloatsuccessProbability":{"Type":"BehaviorDesigner.Runtime.SharedFloat","Name":null,"SinglemValue":0.85},"SharedIntseed":{"Type":"BehaviorDesigner.Runtime.SharedInt","Name":null,"Int32mValue":0},"SharedBooluseSeed":{"Type":"BehaviorDesigner.Runtime.SharedBool","Name":null,"BooleanmValue":false}},{"Type":"BehaviorDesigner.Runtime.Tasks.Idle","NodeData":{"Offset":"(70,140)"},"ID":44,"Name":"Idle","Instant":true}]},{"Type":"BehaviorDesigner.Runtime.Tasks.Sequence","NodeData":{"Offset":"(240,270)"},"ID":45,"Name":"Sequence","Instant":true,"AbortTypeabortType":"Both","Children":[{"Type":"BehaviorDesigner.Runtime.Tasks.Inverter","NodeData":{"Offset":"(-180,120)"},"ID":46,"Name":"Inverter","Instant":true,"Children":[{"Type":"BehaviorDesigner.Runtime.Tasks.Unity.SharedVariables.CompareSharedString","NodeData":{"Offset":"(0,130)"},"ID":47,"Name":"Compare
        Shared String","Instant":true,"SharedStringvariable":{"Type":"BehaviorDesigner.Runtime.SharedString","Name":"lastAction","IsShared":true,"StringmValue":"idle"},"SharedStringcompareTo":{"Type":"BehaviorDesigner.Runtime.SharedString","Name":null,"StringmValue":"Move"}}]},{"Type":"BehaviorDesigner.Runtime.Tasks.RandomProbability","NodeData":{"Offset":"(-20,130)"},"ID":48,"Name":"Random
        Probability","Instant":true,"SharedFloatsuccessProbability":{"Type":"BehaviorDesigner.Runtime.SharedFloat","Name":null,"SinglemValue":0.8},"SharedIntseed":{"Type":"BehaviorDesigner.Runtime.SharedInt","Name":null,"Int32mValue":0},"SharedBooluseSeed":{"Type":"BehaviorDesigner.Runtime.SharedBool","Name":null,"BooleanmValue":false}},{"Type":"BehaviorDesigner.Runtime.Tasks.Unity.SharedVariables.SetSharedString","NodeData":{"Offset":"(160,140)"},"ID":49,"Name":"Set
        Shared String","Instant":true,"SharedStringtargetValue":{"Type":"BehaviorDesigner.Runtime.SharedString","Name":null,"StringmValue":"Move"},"SharedStringtargetVariable":{"Type":"BehaviorDesigner.Runtime.SharedString","Name":"lastAction","IsShared":true,"StringmValue":"idle"}},{"Type":"BehaviorDesigner.Runtime.Tasks.Unity.UnityAnimator.Play","NodeData":{"Offset":"(300,120)","Comment":"Move
        Back"},"ID":50,"Name":"Play","Instant":true,"SharedGameObjecttargetGameObject":{"Type":"BehaviorDesigner.Runtime.SharedGameObject","Name":null},"SharedStringstateName":{"Type":"BehaviorDesigner.Runtime.SharedString","Name":null,"StringmValue":"MoveBack"},"Int32layer":0,"SinglenormalizedTime":0}]},{"Type":"BehaviorDesigner.Runtime.Tasks.Sequence","NodeData":{"Offset":"(1000,270)"},"ID":51,"Name":"Sequence","Instant":true,"AbortTypeabortType":"Both","Children":[{"Type":"BehaviorDesigner.Runtime.Tasks.Inverter","NodeData":{"Offset":"(-300,120)"},"ID":52,"Name":"Inverter","Instant":true,"Children":[{"Type":"BehaviorDesigner.Runtime.Tasks.Unity.SharedVariables.CompareSharedString","NodeData":{"Offset":"(0,130)"},"ID":53,"Name":"Compare
        Shared String","Instant":true,"SharedStringvariable":{"Type":"BehaviorDesigner.Runtime.SharedString","Name":"lastAction","IsShared":true,"StringmValue":"idle"},"SharedStringcompareTo":{"Type":"BehaviorDesigner.Runtime.SharedString","Name":null,"StringmValue":"Move"}}]},{"Type":"BehaviorDesigner.Runtime.Tasks.RandomProbability","NodeData":{"Offset":"(-160,130)"},"ID":54,"Name":"Random
        Probability","Instant":true,"SharedFloatsuccessProbability":{"Type":"BehaviorDesigner.Runtime.SharedFloat","Name":null,"SinglemValue":0.5},"SharedIntseed":{"Type":"BehaviorDesigner.Runtime.SharedInt","Name":null,"Int32mValue":0},"SharedBooluseSeed":{"Type":"BehaviorDesigner.Runtime.SharedBool","Name":null,"BooleanmValue":false}},{"Type":"BehaviorDesigner.Runtime.Tasks.Unity.SharedVariables.SetSharedString","NodeData":{"Offset":"(0,140)"},"ID":55,"Name":"Set
        Shared String","Instant":true,"SharedStringtargetValue":{"Type":"BehaviorDesigner.Runtime.SharedString","Name":null,"StringmValue":"Move"},"SharedStringtargetVariable":{"Type":"BehaviorDesigner.Runtime.SharedString","Name":"lastAction","IsShared":true,"StringmValue":"idle"}},{"Type":"BehaviorDesigner.Runtime.Tasks.Unity.UnityAnimator.Play","NodeData":{"Offset":"(160,120)","Comment":"Move
        Forward"},"ID":56,"Name":"Play","Instant":true,"SharedGameObjecttargetGameObject":{"Type":"BehaviorDesigner.Runtime.SharedGameObject","Name":null},"SharedStringstateName":{"Type":"BehaviorDesigner.Runtime.SharedString","Name":null,"StringmValue":"MoveForward"},"Int32layer":0,"SinglenormalizedTime":0}]}]}]}]},{"Type":"BehaviorDesigner.Runtime.Tasks.Movement.AstarPathfindingProject.Follow","NodeData":{"Offset":"(170,160)"},"ID":57,"Name":"Follow","Instant":true,"SharedGameObjecttarget":{"Type":"BehaviorDesigner.Runtime.SharedGameObject","Name":"Player","IsShared":true},"SharedFloatmoveDistance":{"Type":"BehaviorDesigner.Runtime.SharedFloat","Name":null,"SinglemValue":3},"SharedFloatspeed":{"Type":"BehaviorDesigner.Runtime.SharedFloat","Name":"RunSpeed","IsShared":true,"SinglemValue":4},"SharedBoolstopOnTaskEnd":{"Type":"BehaviorDesigner.Runtime.SharedBool","Name":null,"BooleanmValue":true},"Booleanuse2DMovement":false}]}]},{"Type":"BehaviorDesigner.Runtime.Tasks.Idle","NodeData":{"Offset":"(80,180)"},"ID":58,"Name":"Idle","Instant":true}]}]},{"Type":"BehaviorDesigner.Runtime.Tasks.Sequence","NodeData":{"Offset":"(120,170)"},"ID":59,"Name":"Sequence","Instant":true,"AbortTypeabortType":"Both","Children":[{"Type":"BehaviorDesigner.Runtime.Tasks.Movement.CanSeeObject","NodeData":{"Offset":"(-120,170)"},"ID":60,"Name":"Can
        See Object","Instant":true,"Booleanm_UsePhysics2D":false,"SharedDetectionModem_DetectionMode":{"Type":"BehaviorDesigner.Runtime.Tasks.Movement.SharedDetectionMode","Name":null,"DetectionModemValue":"Tag"},"SharedGameObjectm_TargetObject":{"Type":"BehaviorDesigner.Runtime.SharedGameObject","Name":null},"SharedGameObjectListm_TargetObjects":{"Type":"BehaviorDesigner.Runtime.SharedGameObjectList","Name":null,"List`1mValue":[]},"SharedStringm_TargetTag":{"Type":"BehaviorDesigner.Runtime.SharedString","Name":null,"StringmValue":"Player"},"SharedLayerMaskm_TargetLayerMask":{"Type":"BehaviorDesigner.Runtime.SharedLayerMask","Name":null,"LayerMaskmValue":0},"Int32m_MaxCollisionCount":200,"LayerMaskm_IgnoreLayerMask":20484,"SharedFloatm_FieldOfViewAngle":{"Type":"BehaviorDesigner.Runtime.SharedFloat","Name":"ViewAngle","IsShared":true,"SinglemValue":360},"SharedFloatm_ViewDistance":{"Type":"BehaviorDesigner.Runtime.SharedFloat","Name":null,"SinglemValue":10},"SharedVector3m_Offset":{"Type":"BehaviorDesigner.Runtime.SharedVector3","Name":null,"Vector3mValue":"(0,1,0)"},"SharedVector3m_TargetOffset":{"Type":"BehaviorDesigner.Runtime.SharedVector3","Name":null,"Vector3mValue":"(0,0,0)"},"SharedFloatm_AngleOffset2D":{"Type":"BehaviorDesigner.Runtime.SharedFloat","Name":null,"SinglemValue":0},"SharedBoolm_UseTargetBone":{"Type":"BehaviorDesigner.Runtime.SharedBool","Name":null,"BooleanmValue":false},"SharedHumanBodyBonesm_TargetBone":{"Type":"BehaviorDesigner.Runtime.SharedHumanBodyBones","Name":null,"HumanBodyBonesmValue":"Hips"},"SharedBoolm_DrawDebugRay":{"Type":"BehaviorDesigner.Runtime.SharedBool","Name":null,"BooleanmValue":true},"SharedBoolm_DisableAgentColliderLayer":{"Type":"BehaviorDesigner.Runtime.SharedBool","Name":null,"BooleanmValue":false},"SharedGameObjectm_ReturnedObject":{"Type":"BehaviorDesigner.Runtime.SharedGameObject","Name":"Player","IsShared":true}},{"Type":"BehaviorDesigner.Runtime.Tasks.Unity.UnityAnimator.SetFloatParameter","NodeData":{"Offset":"(40,180)","Comment":"CombatType"},"ID":61,"Name":"Set
        Float Parameter","Instant":true,"Disabled":true,"SharedGameObjecttargetGameObject":{"Type":"BehaviorDesigner.Runtime.SharedGameObject","Name":null},"SharedStringparamaterName":{"Type":"BehaviorDesigner.Runtime.SharedString","Name":null,"StringmValue":"CombatType"},"SharedFloatfloatValue":{"Type":"BehaviorDesigner.Runtime.SharedFloat","Name":null,"SinglemValue":1},"BooleansetOnce":false},{"Type":"BehaviorDesigner.Runtime.Tasks.Unity.UnityGameObject.SendMessage","NodeData":{"Offset":"(200,180)","Comment":"FindTarget"},"ID":62,"Name":"Send
        Message","Instant":true,"SharedGameObjecttargetGameObject":{"Type":"BehaviorDesigner.Runtime.SharedGameObject","Name":null},"SharedStringmessage":{"Type":"BehaviorDesigner.Runtime.SharedString","Name":null,"StringmValue":"FindTarget"},"SharedGenericVariablevalue":{"Type":"BehaviorDesigner.Runtime.SharedGenericVariable","Name":null,"GenericVariablemValue":{"Stringtype":"SharedGameObject","SharedVariablevalue":{"Type":"BehaviorDesigner.Runtime.SharedGameObject","Name":"Player","IsShared":true}}}}]},{"Type":"BehaviorDesigner.Runtime.Tasks.Movement.AstarPathfindingProject.WanderOrigin","NodeData":{"Offset":"(390,190)"},"ID":63,"Name":"Wander
        Origin","Instant":true,"SharedFloatminWanderDistance":{"Type":"BehaviorDesigner.Runtime.SharedFloat","Name":null,"SinglemValue":1},"SharedFloatmaxWanderDistance":{"Type":"BehaviorDesigner.Runtime.SharedFloat","Name":null,"SinglemValue":2},"SharedFloatwanderRate":{"Type":"BehaviorDesigner.Runtime.SharedFloat","Name":null,"SinglemValue":16},"SharedFloatminPauseDuration":{"Type":"BehaviorDesigner.Runtime.SharedFloat","Name":null,"SinglemValue":5},"SharedFloatmaxPauseDuration":{"Type":"BehaviorDesigner.Runtime.SharedFloat","Name":null,"SinglemValue":10},"SharedInttargetRetries":{"Type":"BehaviorDesigner.Runtime.SharedInt","Name":null,"Int32mValue":5},"SharedVector3storedOriginPos":{"Type":"BehaviorDesigner.Runtime.SharedVector3","Name":"OriginPos","IsShared":true,"Vector3mValue":"(0,0,0)"},"SharedFloatspeed":{"Type":"BehaviorDesigner.Runtime.SharedFloat","Name":"WalkSpeed","IsShared":true,"SinglemValue":1},"SharedBoolstopOnTaskEnd":{"Type":"BehaviorDesigner.Runtime.SharedBool","Name":null,"BooleanmValue":true},"Booleanuse2DMovement":false}]}]},{"Type":"BehaviorDesigner.Runtime.Tasks.Repeater","NodeData":{"Offset":"(430,180)"},"ID":64,"Name":"Repeater","Instant":true,"SharedIntcount":{"Type":"BehaviorDesigner.Runtime.SharedInt","Name":null,"Int32mValue":0},"SharedBoolrepeatForever":{"Type":"BehaviorDesigner.Runtime.SharedBool","Name":null,"BooleanmValue":true},"SharedBoolendOnFailure":{"Type":"BehaviorDesigner.Runtime.SharedBool","Name":null,"BooleanmValue":false},"Children":[{"Type":"BehaviorDesigner.Runtime.Tasks.Sequence","NodeData":{"Offset":"(-10,140)"},"ID":65,"Name":"Sequence","Instant":true,"AbortTypeabortType":"None","Children":[{"Type":"BehaviorDesigner.Runtime.Tasks.Unity.SharedVariables.CompareSharedBool","NodeData":{"Offset":"(-100,140)"},"ID":66,"Name":"Compare
        Shared Bool","Instant":true,"SharedBoolvariable":{"Type":"BehaviorDesigner.Runtime.SharedBool","Name":"CanAttack","IsShared":true,"BooleanmValue":true},"SharedBoolcompareTo":{"Type":"BehaviorDesigner.Runtime.SharedBool","Name":null,"BooleanmValue":false}},{"Type":"BehaviorDesigner.Runtime.Tasks.Wait","NodeData":{"Offset":"(70,160)"},"ID":67,"Name":"Wait","Instant":true,"SharedFloatwaitTime":{"Type":"BehaviorDesigner.Runtime.SharedFloat","Name":null,"SinglemValue":10},"SharedBoolrandomWait":{"Type":"BehaviorDesigner.Runtime.SharedBool","Name":null,"BooleanmValue":true},"SharedFloatrandomWaitMin":{"Type":"BehaviorDesigner.Runtime.SharedFloat","Name":null,"SinglemValue":7},"SharedFloatrandomWaitMax":{"Type":"BehaviorDesigner.Runtime.SharedFloat","Name":null,"SinglemValue":10}},{"Type":"BehaviorDesigner.Runtime.Tasks.Unity.SharedVariables.SetSharedBool","NodeData":{"Offset":"(250,140)","Comment":"Can
        Attack"},"ID":68,"Name":"Set Shared Bool","Instant":true,"SharedBooltargetValue":{"Type":"BehaviorDesigner.Runtime.SharedBool","Name":null,"BooleanmValue":true},"SharedBooltargetVariable":{"Type":"BehaviorDesigner.Runtime.SharedBool","Name":"CanAttack","IsShared":true,"BooleanmValue":true}}]}]}]},"Variables":[{"Type":"BehaviorDesigner.Runtime.SharedFloat","Name":"ViewAngle","IsShared":true,"SinglemValue":360},{"Type":"BehaviorDesigner.Runtime.SharedGameObject","Name":"Player","IsShared":true},{"Type":"BehaviorDesigner.Runtime.SharedBool","Name":"isGround","IsShared":true,"BooleanmValue":false},{"Type":"BehaviorDesigner.Runtime.SharedBool","Name":"CanAttack","IsShared":true,"BooleanmValue":true},{"Type":"BehaviorDesigner.Runtime.SharedString","Name":"lastAction","IsShared":true,"StringmValue":"idle"},{"Type":"BehaviorDesigner.Runtime.SharedFloat","Name":"RunSpeed","IsShared":true,"SinglemValue":4},{"Type":"BehaviorDesigner.Runtime.SharedVector3","Name":"OriginPos","IsShared":true,"Vector3mValue":"(0,0,0)"},{"Type":"BehaviorDesigner.Runtime.SharedFloat","Name":"WalkSpeed","IsShared":true,"SinglemValue":1}]}'
      fieldSerializationData:
        typeName: []
        fieldNameHash: 
        startIndex: 
        dataPosition: 
        unityObjects:
        - {fileID: 11400000, guid: 657f026cd3f1be14fad868a0a75fe3f0, type: 2}
        byteData: 
        byteDataArray: 
      Version: 1.7.12
  gizmoViewMode: 2
  showBehaviorDesignerGizmo: 1
--- !u!114 &4942010423498211908
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8637396182626480232}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c2dd186458660e14c93b6dff4b8ca946, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  openAfterImage: 0
  fadeOut: 1
  afterImagePrefab: {fileID: 7086826345758091317, guid: fec54f340a614304e93ec7a1d78fc5bd, type: 3}
  initAfterImageCount: 10
  keepTime: 5
  intervalFrame: 15
--- !u!114 &5947039667049902097
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8637396182626480232}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9d9d406cc6a4ed14b81c854b9ba8a5fb, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  animationEvents:
  - eventName: ShowAfterImage
    OnAnimationEvent:
      m_PersistentCalls:
        m_Calls:
        - m_Target: {fileID: 4942010423498211908}
          m_TargetAssemblyTypeName: Studio.Effect.AfterImageController, Assembly-CSharp
          m_MethodName: ShowAfterImage
          m_Mode: 1
          m_Arguments:
            m_ObjectArgument: {fileID: 0}
            m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
            m_IntArgument: 0
            m_FloatArgument: 0
            m_StringArgument: 
            m_BoolArgument: 0
          m_CallState: 2
--- !u!1001 &8325984343482374660
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 7418675180849339647}
    m_Modifications:
    - target: {fileID: 6513482790461561323, guid: 0e8755a880eb3de4abeb48bf87c7a460, type: 3}
      propertyPath: m_Name
      value: PhantomWolf_VFX
      objectReference: {fileID: 0}
    - target: {fileID: 6686085222407748835, guid: 0e8755a880eb3de4abeb48bf87c7a460, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6686085222407748835, guid: 0e8755a880eb3de4abeb48bf87c7a460, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6686085222407748835, guid: 0e8755a880eb3de4abeb48bf87c7a460, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6686085222407748835, guid: 0e8755a880eb3de4abeb48bf87c7a460, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6686085222407748835, guid: 0e8755a880eb3de4abeb48bf87c7a460, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6686085222407748835, guid: 0e8755a880eb3de4abeb48bf87c7a460, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6686085222407748835, guid: 0e8755a880eb3de4abeb48bf87c7a460, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6686085222407748835, guid: 0e8755a880eb3de4abeb48bf87c7a460, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6686085222407748835, guid: 0e8755a880eb3de4abeb48bf87c7a460, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6686085222407748835, guid: 0e8755a880eb3de4abeb48bf87c7a460, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 0e8755a880eb3de4abeb48bf87c7a460, type: 3}
--- !u!4 &3405400474577348327 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 6686085222407748835, guid: 0e8755a880eb3de4abeb48bf87c7a460, type: 3}
  m_PrefabInstance: {fileID: 8325984343482374660}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &8773365356484183767
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 7418675180849339647}
    m_Modifications:
    - target: {fileID: -8679921383154817045, guid: 76ac524629e7fa547bf36ad076e60198, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 76ac524629e7fa547bf36ad076e60198, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 76ac524629e7fa547bf36ad076e60198, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 76ac524629e7fa547bf36ad076e60198, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 76ac524629e7fa547bf36ad076e60198, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 76ac524629e7fa547bf36ad076e60198, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 76ac524629e7fa547bf36ad076e60198, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 76ac524629e7fa547bf36ad076e60198, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 76ac524629e7fa547bf36ad076e60198, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 76ac524629e7fa547bf36ad076e60198, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 919132149155446097, guid: 76ac524629e7fa547bf36ad076e60198, type: 3}
      propertyPath: m_Name
      value: PhantomWolf 1
      objectReference: {fileID: 0}
    - target: {fileID: 2571542190472776616, guid: 76ac524629e7fa547bf36ad076e60198, type: 3}
      propertyPath: m_Materials.Array.data[0]
      value: 
      objectReference: {fileID: 2100000, guid: 2c5e111b61c80ef40bb4b5c2bf83b566, type: 2}
    - target: {fileID: 5866666021909216657, guid: 76ac524629e7fa547bf36ad076e60198, type: 3}
      propertyPath: m_ApplyRootMotion
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 76ac524629e7fa547bf36ad076e60198, type: 3}
--- !u!4 &5980367714379284366 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: -6106940954541819559, guid: 76ac524629e7fa547bf36ad076e60198, type: 3}
  m_PrefabInstance: {fileID: 8773365356484183767}
  m_PrefabAsset: {fileID: 0}
--- !u!137 &6516342186579725695 stripped
SkinnedMeshRenderer:
  m_CorrespondingSourceObject: {fileID: 2571542190472776616, guid: 76ac524629e7fa547bf36ad076e60198, type: 3}
  m_PrefabInstance: {fileID: 8773365356484183767}
  m_PrefabAsset: {fileID: 0}
--- !u!4 &9100640372295999804 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: -8679921383154817045, guid: 76ac524629e7fa547bf36ad076e60198, type: 3}
  m_PrefabInstance: {fileID: 8773365356484183767}
  m_PrefabAsset: {fileID: 0}
